-- CombatGunScript.lua
-- LocalScript para a CombatGun que vai no StarterGui

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireCombatGun = remoteEvents:WaitForChild("FireCombatGun")

local equipped = false
local tool = nil
local lastFireTime = 0
local fireRate = 0.3 -- Segundos entre disparos

-- Função para disparar
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < fireRate then
        return -- Cooldown ativo
    end
    
    lastFireTime = currentTime
    
    if not tool or not tool.Parent then return end
    
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:Find<PERSON>irst<PERSON>hild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Calcula direção do disparo
    local camera = workspace.CurrentCamera
    local ray = camera:ScreenPointToRay(mouse.X, mouse.Y)
    local direction = ray.Direction.Unit
    local origin = humanoidRootPart.Position + direction * 5
    
    -- Envia para o servidor
    fireCombatGun:FireServer(origin, direction)
    
    -- Efeito sonoro local
    local fireSound = tool.Handle:FindFirstChild("FireSound")
    if fireSound then
        fireSound:Play()
    end
end

-- Conecta eventos quando a ferramenta é equipada
local function onToolEquipped(equippedTool)
    equipped = true
    tool = equippedTool
    
    -- Conecta eventos de input
    local connection1, connection2
    
    connection1 = UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            fire()
        end
    end)
    
    connection2 = tool.Unequipped:Connect(function()
        equipped = false
        tool = nil
        connection1:Disconnect()
        connection2:Disconnect()
    end)
end

-- Monitora quando CombatGun é equipada
local function checkForCombatGun()
    local character = player.Character
    if not character then return end
    
    local combatGun = character:FindFirstChild("CombatGun")
    if combatGun and combatGun:IsA("Tool") then
        onToolEquipped(combatGun)
    end
end

-- Conecta eventos
player.CharacterAdded:Connect(function()
    wait(1) -- Aguarda ferramentas carregarem
    checkForCombatGun()
    
    -- Monitora mudanças no personagem
    player.Character.ChildAdded:Connect(function(child)
        if child.Name == "CombatGun" and child:IsA("Tool") then
            onToolEquipped(child)
        end
    end)
end)

-- Se o personagem já existe
if player.Character then
    checkForCombatGun()
    
    player.Character.ChildAdded:Connect(function(child)
        if child.Name == "CombatGun" and child:IsA("Tool") then
            onToolEquipped(child)
        end
    end)
end
