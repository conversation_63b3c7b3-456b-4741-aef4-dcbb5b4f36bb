-- CollectorGunServer.lua
-- Script do servidor para processar coleta com CollectorGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollection = remoteEvents:WaitForChild("StartCollection")
local stopCollection = remoteEvents:WaitForChild("StopCollection")

-- Carrega o ResourceManager
local ResourceManager = require(script.Parent.ResourceManager)

-- Configurações
local SHRINK_RATE = 0.05 -- Taxa de encolhimento por frame
local MIN_SIZE_MULTIPLIER = 0.1 -- <PERSON><PERSON><PERSON> mínimo (10% do original)
local COLLECTION_RANGE = 100 -- Distância máxima para coletar

-- <PERSON><PERSON> de coletas ativas
local activeCollections = {} -- [player] = {target = resource, originalSize = Vector3}

-- Função para verificar se o jogador pode coletar o recurso
local function canCollectResource(player, resource)
    if not player.Character then return false end
    if not resource or not resource.Parent then return false end
    
    local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return false end
    
    local resourcePart = resource:FindFirstChild("ResourcePart") or resource.PrimaryPart
    if not resourcePart then return false end
    
    -- Verifica distância
    local distance = (humanoidRootPart.Position - resourcePart.Position).Magnitude
    if distance > COLLECTION_RANGE then return false end
    
    return true
end

-- Função para encolher recurso
local function shrinkResource(resource, originalSize)
    local resourcePart = resource:FindFirstChild("ResourcePart") or resource.PrimaryPart
    if not resourcePart then return false end
    
    local currentSize = resourcePart.Size
    local minSize = originalSize * MIN_SIZE_MULTIPLIER
    
    -- Calcula novo tamanho
    local newSize = Vector3.new(
        math.max(minSize.X, currentSize.X - SHRINK_RATE),
        math.max(minSize.Y, currentSize.Y - SHRINK_RATE),
        math.max(minSize.Z, currentSize.Z - SHRINK_RATE)
    )
    
    resourcePart.Size = newSize
    
    -- Verifica se atingiu tamanho mínimo
    if newSize.X <= minSize.X and newSize.Y <= minSize.Y and newSize.Z <= minSize.Z then
        return true -- Recurso coletado completamente
    end
    
    return false -- Ainda encolhendo
end

-- Função para restaurar tamanho do recurso
local function restoreResource(resource, originalSize)
    local resourcePart = resource:FindFirstChild("ResourcePart") or resource.PrimaryPart
    if not resourcePart then return end
    
    resourcePart.Size = originalSize
end

-- Função para coletar recurso completamente
local function collectResource(player, resource)
    -- Marca jogador como carregando recurso
    if player.Character then
        local humanoid = player.Character:FindFirstChild("Humanoid")
        if humanoid then
            -- Reduz velocidade
            humanoid.WalkSpeed = 8 -- Metade da velocidade normal
            
            -- Adiciona tag de carregando
            local carryingTag = Instance.new("BoolValue")
            carryingTag.Name = "CarryingResource"
            carryingTag.Value = true
            carryingTag.Parent = player.Character
            
            print(player.Name .. " coletou um recurso e está carregando")
        end
    end
    
    -- Remove recurso do mundo
    resource:Destroy()
end

-- Inicia coleta
startCollection.OnServerEvent:Connect(function(player, resource)
    -- Valida parâmetros
    if not resource or not resource.Parent then return end
    if not canCollectResource(player, resource) then return end

    -- Verifica se o jogador tem a CollectorGun equipada
    local character = player.Character
    if not character then return end

    local collectorGun = character:FindFirstChild("CollectorGun")
    if not collectorGun then return end

    -- Verifica se já está coletando algo
    if activeCollections[player] then return end

    -- Usa o ResourceManager para iniciar coleta
    if ResourceManager.startCollecting(resource, player) then
        -- Obtém tamanho original
        local originalSizeValue = resource:FindFirstChild("OriginalSize")
        local originalSize

        if originalSizeValue then
            originalSize = originalSizeValue.Value
        else
            local resourcePart = resource:FindFirstChild("ResourcePart") or resource.PrimaryPart
            if resourcePart then
                originalSize = resourcePart.Size
                -- Cria valor para referência futura
                local sizeValue = Instance.new("Vector3Value")
                sizeValue.Name = "OriginalSize"
                sizeValue.Value = originalSize
                sizeValue.Parent = resource
            else
                return
            end
        end

        -- Inicia coleta local também
        activeCollections[player] = {
            target = resource,
            originalSize = originalSize
        }

        print(player.Name .. " iniciou coleta de " .. resource.Name)
    end
end)

-- Para coleta
stopCollection.OnServerEvent:Connect(function(player, resource)
    local collection = activeCollections[player]
    if not collection then return end

    -- Para coleta no ResourceManager
    ResourceManager.stopCollecting(collection.target)

    -- Restaura tamanho original
    if collection.target and collection.target.Parent then
        restoreResource(collection.target, collection.originalSize)
    end

    -- Remove da lista de coletas ativas
    activeCollections[player] = nil

    print(player.Name .. " parou de coletar")
end)

-- Loop principal de coleta
RunService.Heartbeat:Connect(function()
    for player, collection in pairs(activeCollections) do
        local resource = collection.target
        local originalSize = collection.originalSize
        
        -- Verifica se ainda pode coletar
        if not canCollectResource(player, resource) then
            -- Para coleta automaticamente
            if resource and resource.Parent then
                restoreResource(resource, originalSize)
            end
            activeCollections[player] = nil
            continue
        end
        
        -- Encolhe recurso
        local isCompleted = shrinkResource(resource, originalSize)
        
        if isCompleted then
            -- Coleta completa
            collectResource(player, resource)
            activeCollections[player] = nil
        end
    end
end)

-- Limpa coletas quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    local collection = activeCollections[player]
    if collection and collection.target and collection.target.Parent then
        restoreResource(collection.target, collection.originalSize)
    end
    activeCollections[player] = nil
end)
