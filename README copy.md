# Jogo de Arena e Sobrevivência - Roblox Studio

## Descrição
Este é um jogo de arena e sobrevivência multiplayer para Roblox Studio com sistema de bases reivindicáveis e formação de duplas. O jogo suporta até 12 bases simultâneas em um sistema "cada um por si" (Free-for-All).

DESCRIÇAO DO MEU JOGO COMPLETO :

FASE 1: A FUNDAÇÃO DA ARENA (ESTRUTURA MULTI-BASES)

Tarefa 1.1: Bases Reivindicáveis e Times por Cor
"Crie o ambiente inicial do jogo com múltiplas bases individuais que os jogadores podem reivindicar para formar suas próprias equipes.

    Modelo de Base: Em ServerStorage, crie um Model chamado BaseTemplate. Dentro dele, inclua:

        Um Part cilíndrico chamado BasePlatform (o chão de grama).

        No centro, um Part alto e fino chamado CoreTower com uma bandeira no topo.

        Um SpawnLocation com a propriedade Enabled definida como false.

        Uma Part esférica e semi-transparente chamada Barrier.

        Um Part no chão na frente da base chamado ClaimPad.

        Valores da Base: Dentro do modelo, adicione um ObjectValue chamado Owner, um ObjectValue chamado Partner, um NumberValue chamado BaseSize (valor inicial 100), e um IntValue chamado BuildingMaterials (valor inicial 0).

    Gerenciador de Bases (BaseManager): Crie um script em ServerScriptService com este nome. Este script deve:

        Ao iniciar, clonar e posicionar várias instâncias do BaseTemplate pelo mapa.

        Criar uma lista de cores de time disponíveis (ex: BrickColor.new("Bright red"), BrickColor.new("Bright blue"), BrickColor.new("Lime green")).

        Programar o evento .Touched para cada ClaimPad. Quando um jogador sem base toca em um ClaimPad vago, o script deve atribuir o jogador como Owner, escolher uma cor da lista, aplicar a cor à base e ao uniforme do jogador, e ativar o SpawnLocation para ele.

    Sistema de Duplas: Use RemoteEvents para criar um sistema de convite. Um Owner pode convidar um jogador sem base para ser seu Partner. Se aceito, o parceiro se junta à equipe, ganha a cor do time e compartilha o spawn."

FASE 2: O LOOP DE AÇÃO CENTRAL

Tarefa 2.1: Ferramenta de Coleta e Depósito de Recursos
"Implemente a mecânica de coleta por encolhimento e o sistema de depósito de recursos.

    Ferramentas: Em StarterPack, crie duas Tools: CombatGun e CollectorGun.

    Recursos Coletáveis: Na Workspace, espalhe Parts (pedras, caixas) como recursos, cada um com um NumberValue chamado OriginalSize.

    Lógica da CollectorGun: Crie um LocalScript na CollectorGun. Ao segurar o botão do mouse, ele dispara um Raycast. Se atingir um recurso, um RemoteEvent informa ao servidor para encolher o Part. Ao soltar, o Part volta a crescer. Quando o Part atinge um tamanho mínimo, ele é destruído, e o jogador recebe um estado 'CarregandoRecurso' e fica lento.

    Lógica de Depósito: Quando um jogador com 'CarregandoRecurso' entra na Barrier de sua equipe, o recurso é depositado. O script do servidor deve:

        Aumentar o BaseSize da equipe em +15.

        Aumentar os BuildingMaterials da equipe em +10.

        Remover o estado de 'carregando' e a lentidão do jogador.

        Disparar uma atualização visual para a base."

Tarefa 2.2: Combate, Morte e Consequências
"Implemente o combate PvP, as penalidades por morte e o ataque a bases inimigas.

    CombatGun: Programe esta arma para disparar um projétil que causa dano a jogadores de outras equipes.

    Ataque à Base com CollectorGun: Atualize a lógica da CollectorGun. Se seu raio atingir a Barrier ou BasePlatform de uma base inimiga, o servidor deve reduzir lentamente o BaseSize da base inimiga.

    Morte e Penalidade: No BaseManager, conecte-se ao evento Humanoid.Died. Quando um jogador morre:

        Encontre a base da qual ele faz parte.

        Reduza o BaseSize dessa base em 20.

        Se o jogador estava 'CarregandoRecurso', o recurso é perdido.

        Implemente o respawn com timer: 10 segundos para solo, 15 segundos para dupla."

FASE 3: A BASE VIVA E DINÂMICA

Tarefa 3.1: Expansão, Encolhimento e Efeitos da Barreira
"Faça a base crescer e encolher visualmente e implemente os efeitos da barreira.

    Módulo de Controle (BaseController): Crie um ModuleScript em ServerScriptService. Nele, crie uma função UpdateBase(baseModel).

    Lógica da Função: Esta função deve ler o BaseSize do modelo e ajustar o Size da Barrier e da BasePlatform para corresponder. Além disso, ela deve verificar a condição de derrota e destruição de construções. Chame esta função sempre que o BaseSize mudar.

    Efeitos da Barreira: No BaseManager, crie um loop que a cada segundo verifica a posição dos jogadores. Se um jogador estiver dentro de uma barreira inimiga, ele toma dano. Se estiver na sua própria barreira, ele é curado."

Tarefa 3.2: Condição de Derrota e Destruição de Construções
"Implemente a condição final de derrota e a destruição de itens construídos.

    Lógica de Derrota: Dentro da função UpdateBase do BaseController, após ajustar o tamanho, verifique se a borda da Barrier tocou ou passou da CoreTower. Se sim, a base é destruída (cores resetadas, posse removida, fica disponível para ser reivindicada novamente).

    Lógica de Destruição: Também dentro de UpdateBase, após encolher a base, itere por todas as construções pertencentes àquela base. Se a nova borda da Barrier estiver tocando em alguma construção, destrua-a. Isso torna a perda de BaseSize uma ameaça estratégica às defesas construídas."

FASE 4: CONSTRUÇÃO E INTERFACE FINAL

Tarefa 4.1: Construção Estratégica com Materiais
"Implemente um sistema de construção que utiliza um estoque de materiais, separando-o do tamanho da base.

    Interface de Construção: Crie uma ScreenGui visível apenas dentro da própria barreira, com botões para estruturas (ex: 'Muro', 'Torreta'). Cada botão deve mostrar o custo em BuildingMaterials.

    Lógica de Construção: Use um RemoteEvent. Quando um jogador clica para construir, o servidor verifica se a equipe tem BuildingMaterials suficientes. Se sim, deduz o custo, cria a estrutura e a associa à base. Se não, informa o jogador do erro."

Tarefa 4.2: Interface do Usuário (HUD) Completa
"Crie a interface final para o jogador, mostrando todas as informações vitais.

    HUD Principal: Crie uma ScreenGui visível o tempo todo para o HUD. Ela deve mostrar:

        A barra de vida do jogador.

        Uma barra ou número mostrando o BaseSize atual da sua equipe.

        Um ícone com um número ao lado, mostrando a quantidade de BuildingMaterials que sua equipe possui."