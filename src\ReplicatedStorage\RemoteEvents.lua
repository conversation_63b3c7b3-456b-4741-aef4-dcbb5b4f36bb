-- RemoteEvents.lua
-- Script para criar os RemoteEvents necessários para o sistema de convites

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Cria pasta para organizar os eventos
local remoteEventsFolder = Instance.new("Folder")
remoteEventsFolder.Name = "RemoteEvents"
remoteEventsFolder.Parent = ReplicatedStorage

-- RemoteEvent para enviar convite
local sendInviteEvent = Instance.new("RemoteEvent")
sendInviteEvent.Name = "SendInvite"
sendInviteEvent.Parent = remoteEventsFolder

-- RemoteEvent para responder ao convite (aceitar/recusar)
local respondInviteEvent = Instance.new("RemoteEvent")
respondInviteEvent.Name = "RespondInvite"
respondInviteEvent.Parent = remoteEventsFolder

-- RemoteEvent para atualizar a UI
local updateInviteUIEvent = Instance.new("RemoteEvent")
updateInviteUIEvent.Name = "UpdateInviteUI"
updateInviteUIEvent.Parent = remoteEventsFolder

-- RemoteEvents para sistema de coleta
local startCollectingEvent = Instance.new("RemoteEvent")
startCollectingEvent.Name = "StartCollecting"
startCollectingEvent.Parent = remoteEventsFolder

local stopCollectingEvent = Instance.new("RemoteEvent")
stopCollectingEvent.Name = "StopCollecting"
stopCollectingEvent.Parent = remoteEventsFolder

local collectResourceEvent = Instance.new("RemoteEvent")
collectResourceEvent.Name = "CollectResource"
collectResourceEvent.Parent = remoteEventsFolder

-- RemoteEvents para sistema de combate
local fireWeaponEvent = Instance.new("RemoteEvent")
fireWeaponEvent.Name = "FireWeapon"
fireWeaponEvent.Parent = remoteEventsFolder

local playerDamagedEvent = Instance.new("RemoteEvent")
playerDamagedEvent.Name = "PlayerDamaged"
playerDamagedEvent.Parent = remoteEventsFolder

-- RemoteEvents para sistema de depósito
local depositResourceEvent = Instance.new("RemoteEvent")
depositResourceEvent.Name = "DepositResource"
depositResourceEvent.Parent = remoteEventsFolder

-- RemoteEvents para ataque à base
local startBaseAttackEvent = Instance.new("RemoteEvent")
startBaseAttackEvent.Name = "StartBaseAttack"
startBaseAttackEvent.Parent = remoteEventsFolder

local stopBaseAttackEvent = Instance.new("RemoteEvent")
stopBaseAttackEvent.Name = "StopBaseAttack"
stopBaseAttackEvent.Parent = remoteEventsFolder

-- RemoteEvents para sistema de construção
local requestBuildEvent = Instance.new("RemoteEvent")
requestBuildEvent.Name = "RequestBuild"
requestBuildEvent.Parent = remoteEventsFolder

local buildResponseEvent = Instance.new("RemoteEvent")
buildResponseEvent.Name = "BuildResponse"
buildResponseEvent.Parent = remoteEventsFolder

local updateBaseInfoEvent = Instance.new("RemoteEvent")
updateBaseInfoEvent.Name = "UpdateBaseInfo"
updateBaseInfoEvent.Parent = remoteEventsFolder

-- RemoteEvents para sistema de dash
local dashEvent = Instance.new("RemoteEvent")
dashEvent.Name = "Dash"
dashEvent.Parent = remoteEventsFolder

-- RemoteEvents específicos para as armas
local fireCombatGunEvent = Instance.new("RemoteEvent")
fireCombatGunEvent.Name = "FireCombatGun"
fireCombatGunEvent.Parent = remoteEventsFolder

local startCollectionEvent = Instance.new("RemoteEvent")
startCollectionEvent.Name = "StartCollection"
startCollectionEvent.Parent = remoteEventsFolder

local stopCollectionEvent = Instance.new("RemoteEvent")
stopCollectionEvent.Name = "StopCollection"
stopCollectionEvent.Parent = remoteEventsFolder

print("RemoteEvents criados com sucesso!")

-- Retorna true para indicar sucesso
return true