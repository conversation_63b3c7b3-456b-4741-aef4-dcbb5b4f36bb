-- CollectorGunScript.lua
-- LocalScript para a CollectorGun que vai no StarterGui

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollection = remoteEvents:WaitForChild("StartCollection")
local stopCollection = remoteEvents:WaitForChild("StopCollection")

local equipped = false
local tool = nil
local collecting = false
local currentTarget = nil
local beam = nil

-- Função para criar o beam visual
local function createBeam(origin, target)
    if beam then
        beam:Destroy()
    end
    
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Cria attachments
    local attachment0 = Instance.new("Attachment")
    attachment0.Parent = humanoidRootPart
    attachment0.Position = Vector3.new(0, 0, -2)
    
    local attachment1 = Instance.new("Attachment")
    attachment1.Parent = target
    
    -- Cria beam
    beam = Instance.new("Beam")
    beam.Attachment0 = attachment0
    beam.Attachment1 = attachment1
    beam.Color = ColorSequence.new(Color3.new(0, 1, 1))
    beam.Width0 = 0.5
    beam.Width1 = 0.5
    beam.Transparency = NumberSequence.new(0.3)
    beam.Parent = humanoidRootPart
end

-- Função para remover o beam
local function removeBeam()
    if beam then
        beam:Destroy()
        beam = nil
    end
end

-- Função para iniciar coleta
local function startCollecting()
    if collecting then return end
    
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Raycast para encontrar recurso
    local camera = workspace.CurrentCamera
    local ray = camera:ScreenPointToRay(mouse.X, mouse.Y)
    
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(ray.Origin, ray.Direction * 500, raycastParams)
    
    if raycastResult then
        local hit = raycastResult.Instance
        
        -- Verifica se é um recurso coletável
        if hit.Parent and hit.Parent:FindFirstChild("OriginalSize") then
            currentTarget = hit.Parent
            collecting = true
            
            -- Cria beam visual
            createBeam(humanoidRootPart.Position, hit)
            
            -- Envia para servidor
            startCollection:FireServer(currentTarget)
        end
    end
end

-- Função para parar coleta
local function stopCollecting()
    if not collecting then return end
    
    collecting = false
    removeBeam()
    
    if currentTarget then
        stopCollection:FireServer(currentTarget)
        currentTarget = nil
    end
end

-- Conecta eventos quando a ferramenta é equipada
local function onToolEquipped(equippedTool)
    equipped = true
    tool = equippedTool
    
    -- Conecta eventos de input
    local connection1, connection2, connection3
    
    connection1 = UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            startCollecting()
        end
    end)
    
    connection2 = UserInputService.InputEnded:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            stopCollecting()
        end
    end)
    
    connection3 = tool.Unequipped:Connect(function()
        equipped = false
        tool = nil
        stopCollecting()
        connection1:Disconnect()
        connection2:Disconnect()
        connection3:Disconnect()
    end)
end

-- Monitora quando CollectorGun é equipada
local function checkForCollectorGun()
    local character = player.Character
    if not character then return end
    
    local collectorGun = character:FindFirstChild("CollectorGun")
    if collectorGun and collectorGun:IsA("Tool") then
        onToolEquipped(collectorGun)
    end
end

-- Conecta eventos
player.CharacterAdded:Connect(function()
    wait(1) -- Aguarda ferramentas carregarem
    checkForCollectorGun()
    
    -- Monitora mudanças no personagem
    player.Character.ChildAdded:Connect(function(child)
        if child.Name == "CollectorGun" and child:IsA("Tool") then
            onToolEquipped(child)
        end
    end)
end)

-- Se o personagem já existe
if player.Character then
    checkForCollectorGun()
    
    player.Character.ChildAdded:Connect(function(child)
        if child.Name == "CollectorGun" and child:IsA("Tool") then
            onToolEquipped(child)
        end
    end)
end
