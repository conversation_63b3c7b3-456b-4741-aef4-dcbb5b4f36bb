-- CombatGunServer.lua
-- Script do servidor para processar disparos da CombatGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Debris = game:GetService("DebrisService")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireCombatGun = remoteEvents:WaitForChild("FireCombatGun")

-- Configurações
local DAMAGE_AMOUNT = 25
local PROJECTILE_SPEED = 200
local PROJECTILE_LIFETIME = 3
local FIRE_RATE = 0.3

-- Cache de último disparo por jogador
local lastFireTime = {}

-- Função para verificar se jogadores são inimigos
local function areEnemies(player1, player2)
    if player1 == player2 then return false end
    
    -- Verifica se estão na mesma base
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:Find<PERSON>irstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and partner then
                local isPlayer1InBase = (owner.Value == player1) or (partner.Value == player1)
                local isPlayer2InBase = (owner.Value == player2) or (partner.Value == player2)
                
                if isPlayer1InBase and isPlayer2InBase then
                    return false -- Mesma equipe
                end
            end
        end
    end
    
    return true -- Inimigos
end

-- Função para criar projétil
local function createProjectile(origin, direction, shooter)
    local projectile = Instance.new("Part")
    projectile.Name = "CombatProjectile"
    projectile.Size = Vector3.new(0.5, 0.5, 2)
    projectile.Shape = Enum.PartType.Cylinder
    projectile.Material = Enum.Material.Neon
    projectile.BrickColor = BrickColor.new("Bright red")
    projectile.CanCollide = false
    projectile.Anchored = false
    projectile.Position = origin
    projectile.Parent = workspace
    
    -- Orientação do projétil
    projectile.CFrame = CFrame.lookAt(origin, origin + direction)
    
    -- Velocidade
    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
    bodyVelocity.Velocity = direction * PROJECTILE_SPEED
    bodyVelocity.Parent = projectile
    
    -- Efeito visual
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0, 0)
    light.Brightness = 2
    light.Range = 10
    light.Parent = projectile
    
    -- Conexão de colisão
    local connection
    connection = projectile.Touched:Connect(function(hit)
        local hitCharacter = hit.Parent
        local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
        
        -- Ignora o próprio atirador
        if hitPlayer == shooter then return end
        
        -- Se atingiu um jogador
        if hitPlayer and hitHumanoid and areEnemies(shooter, hitPlayer) then
            -- Aplica dano
            hitHumanoid.Health = math.max(0, hitHumanoid.Health - DAMAGE_AMOUNT)
            
            -- Efeito de impacto
            local explosion = Instance.new("Explosion")
            explosion.Position = projectile.Position
            explosion.BlastRadius = 5
            explosion.BlastPressure = 0
            explosion.Parent = workspace
            
            print(shooter.Name .. " atingiu " .. hitPlayer.Name .. " causando " .. DAMAGE_AMOUNT .. " de dano")
            
            -- Destrói projétil
            connection:Disconnect()
            projectile:Destroy()
        elseif hit.Name ~= "Baseplate" and hit.Parent ~= shooter.Character then
            -- Atingiu algo sólido (não baseplate)
            connection:Disconnect()
            projectile:Destroy()
        end
    end)
    
    -- Remove projétil após tempo limite
    Debris:AddItem(projectile, PROJECTILE_LIFETIME)
end

-- Processa disparo da CombatGun
fireCombatGun.OnServerEvent:Connect(function(player, origin, direction)
    -- Verifica rate limiting
    local currentTime = tick()
    if lastFireTime[player] and currentTime - lastFireTime[player] < FIRE_RATE then
        return -- Muito rápido
    end
    lastFireTime[player] = currentTime
    
    -- Valida parâmetros
    if not origin or not direction then return end
    if typeof(origin) ~= "Vector3" or typeof(direction) ~= "Vector3" then return end
    
    -- Verifica se o jogador tem a CombatGun equipada
    local character = player.Character
    if not character then return end
    
    local combatGun = character:FindFirstChild("CombatGun")
    if not combatGun then return end
    
    -- Cria projétil
    createProjectile(origin, direction.Unit, player)
    
    print(player.Name .. " disparou CombatGun")
end)
